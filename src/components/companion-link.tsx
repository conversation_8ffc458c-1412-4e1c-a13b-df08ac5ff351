'use client';
import { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Check, Copy, Share2 } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Label } from './ui/label';

export function CompanionLink({ sessionId }: { sessionId: string }) {
  const [hasCopied, setHasCopied] = useState(false);
  const [origin, setOrigin] = useState('');

  useEffect(() => {
    setOrigin(window.location.origin);
  }, []);

  const companionUrl = `${origin}/companion/${sessionId}`;

  const handleCopy = () => {
    navigator.clipboard.writeText(companionUrl);
    setHasCopied(true);
    setTimeout(() => setHasCopied(false), 2000);
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="icon">
          <Share2 className="h-5 w-5" />
          <span className="sr-only">Share Session</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent align="end" className="w-80">
        <div className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium leading-none">Share Session</h4>
            <p className="text-sm text-muted-foreground">
              Share this link with a friend to follow your timer.
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Input
              value={companionUrl}
              readOnly
              className="h-9"
            />
            <Button onClick={handleCopy} size="icon" className="h-9 w-9">
              {hasCopied ? (
                <Check className="h-4 w-4" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
