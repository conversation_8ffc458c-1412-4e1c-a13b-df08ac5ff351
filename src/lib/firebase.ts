// Import the functions you need from the SDKs you need
import { initializeApp, getApp, type FirebaseOptions } from 'firebase/app';
import { getDatabase } from 'firebase/database';
import { getAuth, signInAnonymously } from 'firebase/auth';
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
const firebaseConfig: FirebaseOptions = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  databaseURL: process.env.NEXT_PUBLIC_FIREBASE_DATABASE_URL,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
};

function getAnonymousAuth() {
  const auth = getAuth(firebaseApp);
  auth.onAuthStateChanged((user) => {
    if (!user) {
      // signInAnonymously will create a new anonymous user if one does not exist
      // or sign in the current anonymous user if they do exist.
      signInAnonymously(auth);
    }
  });
  return auth;
}

function createFirebaseApp(config: FirebaseOptions) {
  try {
    return getApp();
  } catch {
    return initializeApp(config);
  }
}

// Initialize Firebase
export const firebaseApp = createFirebaseApp(firebaseConfig);
export const database = getDatabase(firebaseApp);
getAnonymousAuth();
