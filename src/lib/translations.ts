
export const translations = {
  en: {
    'app.name': '<PERSON>esto<PERSON><PERSON>',
    'timer.focus': 'Focus',
    'timer.short_break': 'Short Break',
    'timer.long_break': 'Long Break',
    'controls.start': 'Start',
    'controls.pause': 'Pause',
    'controls.skip': 'Skip',
    'controls.add_minute': '+1 min',
    'controls.add_five_minutes': '+5 min',
    'controls.auto_start': 'Auto-start next period',
    'controls.auto_starting': 'Auto-starting...',
    'settings.title': 'Settings',
    'settings.timer': 'Timer',
    'settings.appearance': 'Appearance',
    'settings.sounds': 'Sounds',
    'settings.notifications': 'Notifications',
    'settings.timer.focus_duration': 'Focus duration (minutes)',
    'settings.timer.short_break_duration': 'Short break duration (minutes)',
    'settings.timer.long_break_duration': 'Long break duration (minutes)',
    'settings.timer.sessions_before_long_break': 'Sessions before long break',
    'settings.appearance.theme': 'Theme',
    'settings.appearance.theme.light': 'Light',
    'settings.appearance.theme.dark': 'Dark',
    'settings.appearance.theme.system': 'System',
    'settings.appearance.color_palette': 'Color Palette',
    'settings.appearance.color_palette.default': 'Pesto (Default)',
    'settings.appearance.color_palette.forest': 'Forest',
    'settings.appearance.color_palette.ocean': 'Ocean',
    'settings.appearance.color_palette.sunset': 'Sunset',
    'settings.appearance.color_palette.rose': 'Rose',
    'settings.appearance.clock_style': 'Clock Style',
    'settings.appearance.clock_style.digital': 'Digital',
    'settings.appearance.clock_style.analog': 'Analog',
    'settings.appearance.clock_style.vintage': 'Vintage',
    'settings.appearance.clock_style.flip': 'Flip',
    'settings.appearance.backgrounds': 'Backgrounds',
    'settings.appearance.backgrounds.focus_url': 'Focus Background URL',
    'settings.appearance.backgrounds.short_break_url': 'Short Break Background URL',
    'settings.appearance.backgrounds.long_break_url': 'Long Break Background URL',
    'settings.sounds.focus_end': 'Focus End Sound',
    'settings.sounds.short_break_end': 'Short Break End Sound',
    'settings.sounds.long_break_end': 'Long Break End Sound',
    'settings.notifications.enable': 'Enable Push Notifications',
    'settings.save': 'Save',
    'settings.saved': 'Settings saved!',
    'history.title': 'History',
    'history.table.type': 'Type',
    'history.table.duration': 'Duration (min)',
    'history.table.date': 'Date',
    'history.empty': 'No sessions recorded yet.',
    'notification.focus.title': 'Time for a break!',
    'notification.focus.body': 'Your focus session has ended. Well done!',
    'notification.break.title': 'Back to focus!',
    'notification.break.body': 'Your break is over. Time to get back to work.',
    'settings.history.view_history': 'View Session History',
  },
  es: {
    'app.name': 'PestoApp',
    'timer.focus': 'Concentración',
    'timer.short_break': 'Descanso Corto',
    'timer.long_break': 'Descanso Largo',
    'controls.start': 'Empezar',
    'controls.pause': 'Pausar',
    'controls.skip': 'Saltar',
    'controls.add_minute': '+1 min',
    'controls.add_five_minutes': '+5 min',
    'controls.auto_start': 'Auto-iniciar siguiente período',
    'controls.auto_starting': 'Auto-iniciando...',
    'settings.title': 'Configuración',
    'settings.timer': 'Temporizador',
    'settings.appearance': 'Apariencia',
    'settings.sounds': 'Sonidos',
    'settings.notifications': 'Notificaciones',
    'settings.timer.focus_duration': 'Duración de concentración (minutos)',
    'settings.timer.short_break_duration': 'Duración de descanso corto (minutos)',
    'settings.timer.long_break_duration': 'Duración de descanso largo (minutos)',
    'settings.timer.sessions_before_long_break': 'Sesiones antes de descanso largo',
    'settings.appearance.theme': 'Tema',
    'settings.appearance.theme.light': 'Claro',
    'settings.appearance.theme.dark': 'Oscuro',
    'settings.appearance.theme.system': 'Sistema',
    'settings.appearance.color_palette': 'Paleta de Colores',
    'settings.appearance.color_palette.default': 'Pesto (Predeterminado)',
    'settings.appearance.color_palette.forest': 'Bosque',
    'settings.appearance.color_palette.ocean': 'Océano',
    'settings.appearance.color_palette.sunset': 'Atardecer',
    'settings.appearance.color_palette.rose': 'Rosa',
    'settings.appearance.clock_style': 'Estilo de Reloj',
    'settings.appearance.clock_style.digital': 'Digital',
    'settings.appearance.clock_style.analog': 'Analógico',
    'settings.appearance.clock_style.vintage': 'Vintage',
    'settings.appearance.clock_style.flip': 'Flip',
    'settings.appearance.backgrounds': 'Fondos',
    'settings.appearance.backgrounds.focus_url': 'URL Fondo de Concentración',
    'settings.appearance.backgrounds.short_break_url': 'URL Fondo de Descanso Corto',
    'settings.appearance.backgrounds.long_break_url': 'URL Fondo de Descanso Largo',
    'settings.sounds.focus_end': 'Sonido Fin de Concentración',
    'settings.sounds.short_break_end': 'Sonido Fin de Descanso Corto',
    'settings.sounds.long_break_end': 'Sonido Fin de Descanso Largo',
    'settings.notifications.enable': 'Activar Notificaciones Push',
    'settings.save': 'Guardar',
    'settings.saved': '¡Configuración guardada!',
    'history.title': 'Historial',
    'history.table.type': 'Tipo',
    'history.table.duration': 'Duración (min)',
    'history.table.date': 'Fecha',
    'history.empty': 'Aún no hay sesiones grabadas.',
    'notification.focus.title': '¡Hora de un descanso!',
    'notification.focus.body': 'Tu sesión de concentración ha terminado. ¡Bien hecho!',
    'notification.break.title': '¡De vuelta a la concentración!',
    'notification.break.body': 'Tu descanso ha terminado. Es hora de volver al trabajo.',
    'settings.history.view_history': 'Ver Historial de Sesiones',
  },
};
