'use client';

import { createContext, useContext, useEffect, useState, type ReactNode, useCallback, useRef } from 'react';
import type { SessionRecord, TimerMode, TimerStateData } from '@/types';
import { useSettings } from './settings-provider';
import * as Tone from 'tone';
import { useLanguage } from './language-provider';
import { HISTORY_KEY } from '@/lib/constants';
import { database, firebaseApp } from '@/lib/firebase';
import { ref, onValue, set, onDisconnect, get } from 'firebase/database';
import { getAuth } from 'firebase/auth';

type TimerProviderState = {
  mode: TimerMode;
  timeLeft: number;
  isActive: boolean;
  sessionCount: number;
  isAutoStarting: boolean;
  isLastTenSeconds: boolean;
  sessionId: string | null;
  isRinging: boolean;
  startTimer: () => void;
  pauseTimer: () => void;
  skipTimer: () => void;
  addMinute: () => void;
  addFiveMinutes: () => void;
};

const TimerProviderContext = createContext<TimerProviderState | undefined>(undefined);

export function TimerProvider({ children }: { children: ReactNode }) {
  const { settings, isLoaded } = useSettings();
  const { t } = useLanguage();
  const [mode, setMode] = useState<TimerMode>('focus');
  const [timeLeft, setTimeLeft] = useState(settings.focusDuration * 60);
  const [isActive, setIsActive] = useState(false);
  const [sessionCount, setSessionCount] = useState(0);
  const [isAutoStarting, setIsAutoStarting] = useState(false);
  const [isLastTenSeconds, setIsLastTenSeconds] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isRinging, setIsRinging] = useState(false);

  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Generate a unique session ID on mount
 useEffect(() => {
    const auth = getAuth(firebaseApp);
    const anonUser = auth.currentUser;

    let newSessionId: string;
    if (anonUser) {
      newSessionId = anonUser.uid;
    } else {
      newSessionId = Math.random().toString(36).substring(2, 12);
    }

    setSessionId(newSessionId);

    // Setup Firebase presence and data connection
    const sessionRef = ref(database, `sessions/${newSessionId}`);

    get(sessionRef).then((snapshot) => {
      if (snapshot.exists()) {
        const sessionData: TimerStateData = snapshot.val();
        setMode(sessionData.mode);
        setTimeLeft(sessionData.timeLeft);
      } else {
        set(sessionRef, { anonId: anonUser?.uid || null, mode: mode, timeLeft: timeLeft, lastUpdated: Date.now() });
      }
    });

    const ringRef = ref(database, `sessions/${newSessionId}/ring`);
    onValue(ringRef, (snapshot) => {
        if(snapshot.exists() && snapshot.val() === true) {
            setIsRinging(true);
            setTimeout(() => {
                setIsRinging(false);
                set(ringRef, false); // Reset the ring state
            }, 3000); // Ring for 3 seconds
        }
    });

    return () => {
        // Don't remove the session on disconnect, just update the presence
        onDisconnect(sessionRef).cancel();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Sync state to Firebase
  useEffect(() => {
    if (sessionId) {
      const sessionRef = ref(database, `sessions/${sessionId}`);
      const stateToSync: TimerStateData = {
        mode,
        timeLeft,
        lastUpdated: Date.now()
      };
      set(sessionRef, stateToSync);
    }
  }, [mode, timeLeft, sessionId]);


  const playSound = useCallback((url: string) => {
    try {
      const player = new Tone.Player(url).toDestination();
      Tone.loaded().then(() => player.start());
    } catch (e) { console.error(e) }
  }, []);

  const showNotification = useCallback((title: string, body: string) => {
    if (settings.notifications.enabled && Notification.permission === 'granted') {
      navigator.serviceWorker.ready.then(registration => {
        registration.showNotification(title, { body, icon: '/icon-192x192.png' });
      });
    }
  }, [settings.notifications.enabled]);

  const saveSession = useCallback((type: TimerMode, duration: number) => {
    const newSession: SessionRecord = {
      id: new Date().toISOString(),
      type,
      duration,
      completedAt: new Date().toISOString(),
    };
    try {
      const history = JSON.parse(localStorage.getItem(HISTORY_KEY) || '[]') as SessionRecord[];
      history.unshift(newSession);
      localStorage.setItem(HISTORY_KEY, JSON.stringify(history.slice(0, 100))); // Limit history size
    } catch (e) { console.error('Failed to save session', e)}
  }, []);

  const nextMode = useCallback(() => {
    setIsActive(false);

    const completedMode = mode;
    const completedDuration =
      completedMode === 'focus' ? settings.focusDuration :
      completedMode === 'shortBreak' ? settings.shortBreakDuration :
      settings.longBreakDuration;
    saveSession(completedMode, completedDuration);

    // Determine next mode and duration
    let nextModeType: TimerMode;
    let nextDuration: number;

    if (completedMode === 'focus') {
      playSound(settings.sounds.focusEnd);
      showNotification(t('notification.focus.title'), t('notification.focus.body'));
      const newSessionCount = sessionCount + 1;
      setSessionCount(newSessionCount);
      if (newSessionCount % settings.sessionsBeforeLongBreak === 0) {
        nextModeType = 'longBreak';
        nextDuration = settings.longBreakDuration * 60;
      } else {
        nextModeType = 'shortBreak';
        nextDuration = settings.shortBreakDuration * 60;
      }
    } else {
      playSound(settings.sounds.shortBreakEnd);
      showNotification(t('notification.break.title'), t('notification.break.body'));
      nextModeType = 'focus';
      nextDuration = settings.focusDuration * 60;
    }

    // Update mode and time atomically using a single batch
    setMode(nextModeType);
    setTimeLeft(nextDuration);

    if (settings.autoStartNextPeriod) {
      setIsAutoStarting(true);
      // Use a slightly longer delay to ensure all state updates have completed
      setTimeout(() => {
        setIsAutoStarting(false);
        setIsActive(true);
      }, 1500);
    }
  }, [mode, sessionCount, settings, t, saveSession, showNotification, playSound]);

  useEffect(() => {
    if (isActive) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => {
          if (prev <= 1) {
            clearInterval(intervalRef.current!);
            setIsLastTenSeconds(false);
            nextMode();
            return 0;
          }
          if (prev <= 10 && prev > 1) {
            setIsLastTenSeconds(true);
          } else if (prev > 10) {
            setIsLastTenSeconds(false);
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    }
    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current);
    };
  }, [isActive, nextMode]);

  useEffect(() => {
    if (isLoaded && mode === 'focus' && !isActive) {
      // Only update focus duration if we're currently in focus mode and not active
      setTimeLeft(settings.focusDuration * 60);
    }
  }, [settings.focusDuration, isLoaded, mode, isActive]);

  useEffect(() => {
    if (isLoaded && mode === 'shortBreak' && !isActive) {
      // Only update short break duration if we're currently in short break mode and not active
      setTimeLeft(settings.shortBreakDuration * 60);
    }
  }, [settings.shortBreakDuration, isLoaded, mode, isActive]);

  useEffect(() => {
    if (isLoaded && mode === 'longBreak' && !isActive) {
      // Only update long break duration if we're currently in long break mode and not active
      setTimeLeft(settings.longBreakDuration * 60);
    }
  }, [settings.longBreakDuration, isLoaded, mode, isActive]);

  const startTimer = () => {
    Tone.start();
    setIsActive(true);
  };
  const pauseTimer = () => {
    setIsActive(false);
    setIsLastTenSeconds(false); 
  };
  const skipTimer = () => {
    setIsLastTenSeconds(false); 
    nextMode();
  };
  const addMinute = () => {
    setTimeLeft(prev => prev + 60);
    setIsLastTenSeconds(false); 
  };
  const addFiveMinutes = () => {
    setTimeLeft(prev => prev + 300); 
    setIsLastTenSeconds(false); 
  };

  const value = { mode, timeLeft, isActive, sessionCount, isAutoStarting, isLastTenSeconds, sessionId, isRinging, startTimer, pauseTimer, skipTimer, addMinute, addFiveMinutes };

  return <TimerProviderContext.Provider value={value}>{children}</TimerProviderContext.Provider>;
}

export const useTimer = () => {
  const context = useContext(TimerProviderContext);
  if (context === undefined) {
    throw new Error('useTimer must be used within a TimerProvider');
  }
  return context;
};
