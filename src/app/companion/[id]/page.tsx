'use client';

import { useEffect, useState } from 'react';
import { database } from '@/lib/firebase';
import { ref, onValue, set } from 'firebase/database';
import { DigitalClock } from '@/components/clocks/digital-clock';
import { useLanguage } from '@/providers/language-provider';
import type { TimerMode, TimerStateData } from '@/types';
import { Button } from '@/components/ui/button';
import { BellRing, WifiOff } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export default function CompanionPage({ params }: { params: { id: string } }) {
  const sessionId = params?.id;
  const { t } = useLanguage();
  const { toast } = useToast();
  const [timerState, setTimerState] = useState<TimerStateData | null>(null);
  const [lastUpdated, setLastUpdated] = useState<number>(Date.now());
  const [isDisconnected, setIsDisconnected] = useState(false);

  const modeText: Record<TimerMode, string> = {
    focus: t('timer.focus'),
    shortBreak: t('timer.short_break'),
    longBreak: t('timer.long_break'),
  };

  useEffect(() => {
    // Ensure sessionId is defined before attempting to fetch data
 if (!sessionId) {
 return;
    }
    const sessionRef = ref(database, `sessions/${sessionId}`);
    
    const unsubscribe = onValue(sessionRef, (snapshot) => {
      if (snapshot.exists()) {
        setTimerState(snapshot.val());
        setLastUpdated(Date.now());
        setIsDisconnected(false);
      } else {
        setTimerState(null);
        setIsDisconnected(true);
      }
    });

    return () => unsubscribe();
  }, [sessionId]);

  // Check for disconnection
  useEffect(() => {
    const interval = setInterval(() => {
      if (Date.now() - lastUpdated > 20000) { // 20 seconds threshold
        setIsDisconnected(true);
      }
    }, 5000);
    return () => clearInterval(interval);
  }, [lastUpdated]);

  const handleRingBell = async () => {
    try {
        const ringRef = ref(database, `sessions/${sessionId}/ring`);
        await set(ringRef, true);
        toast({
            title: "Bell Rung!",
            description: "Your friend has been notified.",
        });
    } catch(e) {
        console.error("Failed to ring bell", e);
        toast({
            title: "Error",
            description: "Could not ring the bell. Please try again.",
            variant: "destructive",
        });
    }
  };

  if (isDisconnected) {
    return (
      <div className="flex flex-col min-h-screen items-center justify-center bg-background text-foreground p-4">
        <WifiOff className="h-24 w-24 text-destructive mb-4" />
        <h1 className="text-4xl font-bold mb-2">Session Disconnected</h1>
        <p className="text-muted-foreground text-lg text-center">
          The timer session you were following has ended or the user has disconnected.
        </p>
      </div>
    );
  }

  if (!timerState) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-background">
        <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
        <p className="ml-4 text-lg">Connecting to session...</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen items-center justify-center bg-background text-foreground p-4">
      <div className="text-center mb-8">
        <p className="text-2xl font-medium text-foreground tracking-wider uppercase">
          {modeText[timerState.mode]}
        </p>
        <p className="text-muted-foreground">Companion Mode</p>
      </div>

      <DigitalClock time={timerState.timeLeft} />

      <div className="mt-8">
        <Button onClick={handleRingBell} size="lg" className="rounded-full w-24 h-24">
          <BellRing className="w-12 h-12" />
          <span className="sr-only">Ring Bell</span>
        </Button>
      </div>
    </div>
  );
}
